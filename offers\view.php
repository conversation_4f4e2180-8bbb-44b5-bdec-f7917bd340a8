<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// التحقق من وجود معرف العرض
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$offer_id = (int)$_GET['id'];

// جلب بيانات العرض
$sql = "SELECT * FROM offers WHERE id = ? AND user_id = ?";
$offer = fetchRow($sql, 'ii', [$offer_id, $user_id]);

// التحقق من وجود العرض
if (!$offer) {
    header('Location: index.php');
    exit();
}

// جلب إحصائيات العرض
$sql = "SELECT 
            date, 
            clicks, 
            conversions, 
            (conversions * ?) as earnings
        FROM 
            statistics 
        WHERE 
            offer_id = ?
        ORDER BY 
            date DESC
        LIMIT 30";

$statistics = fetchAll($sql, 'di', [$offer['commission'], $offer_id]);

// إنشاء رابط التتبع
$tracking_link = generateTrackingLink($offer_id, $user_id);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($offer['title']); ?> - موقع CPA الاحترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?php echo htmlspecialchars($offer['title']); ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="edit.php?id=<?php echo $offer_id; ?>" class="btn btn-sm btn-warning">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="index.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل العرض -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card shadow mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-info-circle"></i> تفاصيل العرض
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-3 fw-bold">العنوان:</div>
                                    <div class="col-md-9"><?php echo htmlspecialchars($offer['title']); ?></div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-3 fw-bold">الوصف:</div>
                                    <div class="col-md-9"><?php echo nl2br(htmlspecialchars($offer['description'])); ?></div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-3 fw-bold">الرابط الأصلي:</div>
                                    <div class="col-md-9">
                                        <a href="<?php echo htmlspecialchars($offer['original_url']); ?>" target="_blank">
                                            <?php echo htmlspecialchars($offer['original_url']); ?>
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-3 fw-bold">العمولة:</div>
                                    <div class="col-md-9 text-success fw-bold">
                                        $<?php echo number_format($offer['commission'], 2); ?>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-3 fw-bold">الفئة:</div>
                                    <div class="col-md-9">
                                        <span class="badge bg-info">
                                            <?php echo htmlspecialchars($offer['category'] ?: 'غير محدد'); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-3 fw-bold">المتطلبات:</div>
                                    <div class="col-md-9"><?php echo nl2br(htmlspecialchars($offer['requirements'])); ?></div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-3 fw-bold">الحالة:</div>
                                    <div class="col-md-9">
                                        <span class="badge bg-<?php echo $offer['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $offer['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-3 fw-bold">تاريخ الإنشاء:</div>
                                    <div class="col-md-9">
                                        <?php echo date('Y-m-d H:i', strtotime($offer['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- رابط التتبع -->
                        <div class="card shadow mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-link"></i> رابط التتبع
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="input-group mb-3">
                                    <input type="text" class="form-control" id="tracking_link" value="<?php echo $tracking_link; ?>" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyTrackingLink()">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <div class="alert alert-info" role="alert">
                                    <i class="fas fa-info-circle"></i> استخدم هذا الرابط للترويج للعرض وتتبع النقرات والتحويلات.
                                </div>
                            </div>
                        </div>
                        
                        <!-- ملخص الإحصائيات -->
                        <div class="card shadow mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="fas fa-chart-pie"></i> ملخص الإحصائيات
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $total_clicks = 0;
                                $total_conversions = 0;
                                $total_earnings = 0;
                                
                                foreach ($statistics as $stat) {
                                    $total_clicks += $stat['clicks'];
                                    $total_conversions += $stat['conversions'];
                                    $total_earnings += $stat['earnings'];
                                }
                                
                                $conversion_rate = $total_clicks > 0 ? ($total_conversions / $total_clicks) * 100 : 0;
                                ?>
                                
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <h5 class="text-primary"><?php echo number_format($total_clicks); ?></h5>
                                        <div class="text-muted">النقرات</div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h5 class="text-success"><?php echo number_format($total_conversions); ?></h5>
                                        <div class="text-muted">التحويلات</div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h5 class="text-info"><?php echo number_format($conversion_rate, 2); ?>%</h5>
                                        <div class="text-muted">معدل التحويل</div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h5 class="text-success">$<?php echo number_format($total_earnings, 2); ?></h5>
                                        <div class="text-muted">الأرباح</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- جدول الإحصائيات -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-chart-bar"></i> إحصائيات العرض (آخر 30 يوم)
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($statistics)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد إحصائيات حتى الآن</h5>
                                <p class="text-muted">ابدأ بالترويج للعرض باستخدام رابط التتبع</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>النقرات</th>
                                            <th>التحويلات</th>
                                            <th>معدل التحويل</th>
                                            <th>الأرباح</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($statistics as $stat): ?>
                                        <tr>
                                            <td><?php echo $stat['date']; ?></td>
                                            <td><?php echo number_format($stat['clicks']); ?></td>
                                            <td><?php echo number_format($stat['conversions']); ?></td>
                                            <td>
                                                <?php 
                                                $rate = $stat['clicks'] > 0 ? ($stat['conversions'] / $stat['clicks']) * 100 : 0;
                                                echo number_format($rate, 2) . '%';
                                                ?>
                                            </td>
                                            <td class="text-success">
                                                $<?php echo number_format($stat['earnings'], 2); ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function copyTrackingLink() {
            var copyText = document.getElementById("tracking_link");
            copyText.select();
            copyText.setSelectionRange(0, 99999);
            document.execCommand("copy");
            
            alert("تم نسخ الرابط: " + copyText.value);
        }
    </script>
</body>
</html>
