<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من وجود رمز التتبع
if (!isset($_GET['c']) || empty($_GET['c'])) {
    header('HTTP/1.0 404 Not Found');
    exit('رمز التتبع غير صحيح');
}

$tracking_code = $_GET['c'];

// جلب معلومات العرض من رمز التتبع
$sql = "SELECT tc.offer_id, tc.user_id, o.commission, o.title, o.status 
        FROM tracking_codes tc 
        JOIN offers o ON tc.offer_id = o.id 
        WHERE tc.code = ? AND o.status = 'active'";

$tracking = fetchRow($sql, 's', [$tracking_code]);

if (!$tracking) {
    header('HTTP/1.0 404 Not Found');
    exit('العرض غير موجود أو غير نشط');
}

// تسجيل التحويل
$conversion_success = trackConversion($tracking_code);

if ($conversion_success) {
    // تسجيل التحويل في جدول التحويلات
    $ip_address = $_SERVER['REMOTE_ADDR'];
    $amount = $tracking['commission'];
    
    $sql = "INSERT INTO conversions (tracking_code, offer_id, user_id, ip_address, amount, status, converted_at) 
            VALUES (?, ?, ?, ?, ?, 'pending', NOW())";
    executePreparedQuery($sql, 'siisds', [
        $tracking_code, 
        $tracking['offer_id'], 
        $tracking['user_id'], 
        $ip_address, 
        $amount
    ]);
    
    $message = 'تم تسجيل التحويل بنجاح!';
    $success = true;
} else {
    $message = 'فشل في تسجيل التحويل';
    $success = false;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد التحويل - موقع CPA الاحترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .conversion-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="conversion-card">
                    <?php if ($success): ?>
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2 class="text-success mb-3">تهانينا!</h2>
                        <p class="lead mb-4"><?php echo $message; ?></p>
                        <div class="alert alert-success" role="alert">
                            <h5 class="alert-heading">تفاصيل التحويل:</h5>
                            <hr>
                            <p class="mb-1"><strong>العرض:</strong> <?php echo htmlspecialchars($tracking['title']); ?></p>
                            <p class="mb-1"><strong>العمولة:</strong> $<?php echo number_format($tracking['commission'], 2); ?></p>
                            <p class="mb-0"><strong>الحالة:</strong> في انتظار الموافقة</p>
                        </div>
                    <?php else: ?>
                        <div class="error-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <h2 class="text-danger mb-3">خطأ!</h2>
                        <p class="lead mb-4"><?php echo $message; ?></p>
                        <div class="alert alert-danger" role="alert">
                            يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني
                        </div>
                    <?php endif; ?>
                    
                    <a href="public/index.php" class="btn btn-primary">
                        <i class="fas fa-home"></i> العودة للصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
