<?php
/**
 * ملف الدوال المساعدة للموقع
 */

/**
 * دالة للحصول على إحصائيات المستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @return array مصفوفة تحتوي على إحصائيات المستخدم
 */
function getUserStats($user_id) {
    $sql = "SELECT 
                COALESCE(SUM(o.commission * s.conversions), 0) as total_earnings,
                COALESCE(SUM(s.clicks), 0) as total_clicks,
                COALESCE(SUM(s.conversions), 0) as total_conversions,
                COUNT(DISTINCT CASE WHEN o.status = 'active' THEN o.id END) as active_offers
            FROM 
                offers o
            LEFT JOIN 
                statistics s ON o.id = s.offer_id
            WHERE 
                o.user_id = ?";
    
    $stats = fetchRow($sql, 'i', [$user_id]);
    
    if (!$stats) {
        return [
            'total_earnings' => 0,
            'total_clicks' => 0,
            'total_conversions' => 0,
            'active_offers' => 0
        ];
    }
    
    return $stats;
}

/**
 * دالة للحصول على العروض الحديثة للمستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @param int $limit عدد العروض المراد استرجاعها
 * @return array مصفوفة تحتوي على العروض الحديثة
 */
function getRecentOffers($user_id, $limit = 5) {
    $sql = "SELECT 
                o.id, 
                o.title, 
                o.commission, 
                o.status,
                COALESCE(SUM(s.clicks), 0) as clicks,
                COALESCE(SUM(s.conversions), 0) as conversions,
                COALESCE(SUM(o.commission * s.conversions), 0) as earnings
            FROM 
                offers o
            LEFT JOIN 
                statistics s ON o.id = s.offer_id
            WHERE 
                o.user_id = ?
            GROUP BY 
                o.id
            ORDER BY 
                o.created_at DESC
            LIMIT ?";
    
    return fetchAll($sql, 'ii', [$user_id, $limit]);
}

/**
 * دالة للحصول على عرض بواسطة المعرف
 * 
 * @param int $offer_id معرف العرض
 * @return array|null مصفوفة تحتوي على بيانات العرض أو null
 */
function getOfferById($offer_id) {
    $sql = "SELECT * FROM offers WHERE id = ?";
    return fetchRow($sql, 'i', [$offer_id]);
}

/**
 * دالة لإنشاء رابط تتبع للعرض
 * 
 * @param int $offer_id معرف العرض
 * @param int $user_id معرف المستخدم
 * @return string رابط التتبع
 */
function generateTrackingLink($offer_id, $user_id) {
    $base_url = getBaseUrl();
    $tracking_code = md5($offer_id . $user_id . time());
    
    // حفظ رمز التتبع في قاعدة البيانات
    $sql = "INSERT INTO tracking_codes (offer_id, user_id, code) VALUES (?, ?, ?)";
    executePreparedQuery($sql, 'iis', [$offer_id, $user_id, $tracking_code]);
    
    return $base_url . 'track.php?c=' . $tracking_code;
}

/**
 * دالة للحصول على عنوان URL الأساسي للموقع
 * 
 * @return string عنوان URL الأساسي
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_name = dirname($_SERVER['SCRIPT_NAME']);
    
    if ($script_name == '/' || $script_name == '\\') {
        $script_name = '';
    }
    
    return $protocol . '://' . $host . $script_name . '/';
}

/**
 * دالة لتسجيل نقرة على عرض
 * 
 * @param string $tracking_code رمز التتبع
 * @return bool نجاح العملية
 */
function trackClick($tracking_code) {
    // الحصول على معرف العرض من رمز التتبع
    $sql = "SELECT offer_id FROM tracking_codes WHERE code = ?";
    $tracking = fetchRow($sql, 's', [$tracking_code]);
    
    if (!$tracking) {
        return false;
    }
    
    $offer_id = $tracking['offer_id'];
    $date = date('Y-m-d');
    
    // التحقق مما إذا كان هناك سجل إحصائيات لهذا اليوم
    $sql = "SELECT id FROM statistics WHERE offer_id = ? AND date = ?";
    $stat = fetchRow($sql, 'is', [$offer_id, $date]);
    
    if ($stat) {
        // تحديث السجل الموجود
        $sql = "UPDATE statistics SET clicks = clicks + 1 WHERE id = ?";
        executePreparedQuery($sql, 'i', [$stat['id']]);
    } else {
        // إنشاء سجل جديد
        $sql = "INSERT INTO statistics (offer_id, date, clicks, conversions) VALUES (?, ?, 1, 0)";
        executePreparedQuery($sql, 'is', [$offer_id, $date]);
    }
    
    return true;
}

/**
 * دالة لتسجيل تحويل على عرض
 * 
 * @param string $tracking_code رمز التتبع
 * @return bool نجاح العملية
 */
function trackConversion($tracking_code) {
    // الحصول على معرف العرض من رمز التتبع
    $sql = "SELECT offer_id FROM tracking_codes WHERE code = ?";
    $tracking = fetchRow($sql, 's', [$tracking_code]);
    
    if (!$tracking) {
        return false;
    }
    
    $offer_id = $tracking['offer_id'];
    $date = date('Y-m-d');
    
    // التحقق مما إذا كان هناك سجل إحصائيات لهذا اليوم
    $sql = "SELECT id FROM statistics WHERE offer_id = ? AND date = ?";
    $stat = fetchRow($sql, 'is', [$offer_id, $date]);
    
    if ($stat) {
        // تحديث السجل الموجود
        $sql = "UPDATE statistics SET conversions = conversions + 1 WHERE id = ?";
        executePreparedQuery($sql, 'i', [$stat['id']]);
    } else {
        // إنشاء سجل جديد
        $sql = "INSERT INTO statistics (offer_id, date, clicks, conversions) VALUES (?, ?, 0, 1)";
        executePreparedQuery($sql, 'is', [$offer_id, $date]);
    }
    
    return true;
}

/**
 * دالة للتحقق من صحة بيانات المستخدم
 * 
 * @param string $email البريد الإلكتروني
 * @param string $password كلمة المرور
 * @return array|bool بيانات المستخدم أو false
 */
function validateUser($email, $password) {
    $sql = "SELECT id, name, email, password FROM users WHERE email = ?";
    $user = fetchRow($sql, 's', [$email]);
    
    if (!$user) {
        return false;
    }
    
    if (password_verify($password, $user['password'])) {
        unset($user['password']); // إزالة كلمة المرور من المصفوفة المرجعة
        return $user;
    }
    
    return false;
}

/**
 * دالة لتسجيل مستخدم جديد
 * 
 * @param string $name اسم المستخدم
 * @param string $email البريد الإلكتروني
 * @param string $password كلمة المرور
 * @return int|bool معرف المستخدم الجديد أو false
 */
function registerUser($name, $email, $password) {
    // التحقق من وجود البريد الإلكتروني
    $sql = "SELECT id FROM users WHERE email = ?";
    $existing_user = fetchRow($sql, 's', [$email]);
    
    if ($existing_user) {
        return false;
    }
    
    // تشفير كلمة المرور
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // إدراج المستخدم الجديد
    $sql = "INSERT INTO users (name, email, password, created_at) VALUES (?, ?, ?, NOW())";
    $stmt = executePreparedQuery($sql, 'sss', [$name, $email, $hashed_password]);
    
    if (!$stmt) {
        return false;
    }
    
    return getLastInsertId();
}
