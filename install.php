<?php
/**
 * ملف التثبيت التلقائي لموقع CPA الاحترافي
 */

// التحقق من وجود ملف الإعدادات
if (file_exists('config/database.php')) {
    // محاولة الاتصال بقاعدة البيانات
    try {
        require_once 'config/database.php';
        
        // التحقق من وجود الجداول
        $tables_check = $conn->query("SHOW TABLES LIKE 'users'");
        
        if ($tables_check && $tables_check->num_rows > 0) {
            echo "<div style='text-align: center; padding: 50px; font-family: Arial, sans-serif;'>";
            echo "<h2 style='color: #28a745;'>✅ الموقع مثبت بالفعل</h2>";
            echo "<p>يبدو أن الموقع مثبت ويعمل بشكل صحيح.</p>";
            echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى الموقع</a>";
            echo "</div>";
            exit();
        }
    } catch (Exception $e) {
        // خطأ في الاتصال - سنعرض نموذج الإعدادات
    }
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error_message = '';
$success_message = '';

// معالجة الخطوات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($step == 1) {
        // التحقق من متطلبات النظام
        $step = 2;
    } elseif ($step == 2) {
        // إعداد قاعدة البيانات
        $db_host = trim($_POST['db_host']);
        $db_user = trim($_POST['db_user']);
        $db_pass = trim($_POST['db_pass']);
        $db_name = trim($_POST['db_name']);
        
        if (empty($db_host) || empty($db_user) || empty($db_name)) {
            $error_message = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            // اختبار الاتصال
            $test_conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
            
            if ($test_conn->connect_error) {
                $error_message = 'فشل الاتصال بقاعدة البيانات: ' . $test_conn->connect_error;
            } else {
                // حفظ إعدادات قاعدة البيانات
                $config_content = "<?php\n";
                $config_content .= "/**\n * ملف الاتصال بقاعدة البيانات - InfinityFree\n */\n\n";
                $config_content .= "// معلومات الاتصال بقاعدة البيانات - InfinityFree\n";
                $config_content .= "define('DB_HOST', '" . addslashes($db_host) . "');\n";
                $config_content .= "define('DB_USER', '" . addslashes($db_user) . "');\n";
                $config_content .= "define('DB_PASS', '" . addslashes($db_pass) . "');\n";
                $config_content .= "define('DB_NAME', '" . addslashes($db_name) . "');\n\n";

                // إضافة باقي محتوى ملف database.php
                $original_content = file_get_contents('config/database.php');
                $lines = explode("\n", $original_content);
                $start_adding = false;
                foreach ($lines as $line) {
                    if (strpos($line, '// إنشاء اتصال بقاعدة البيانات') !== false) {
                        $start_adding = true;
                    }
                    if ($start_adding) {
                        $config_content .= $line . "\n";
                    }
                }
                
                if (!is_dir('config')) {
                    mkdir('config', 0755, true);
                }
                
                file_put_contents('config/database.php', $config_content);
                
                // تنفيذ ملف SQL
                $sql_content = file_get_contents('database/setup.sql');
                $sql_statements = explode(';', $sql_content);
                
                foreach ($sql_statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $test_conn->query($statement);
                    }
                }
                
                $test_conn->close();
                $step = 3;
                $success_message = 'تم إعداد قاعدة البيانات بنجاح';
            }
        }
    } elseif ($step == 3) {
        // إنشاء حساب المدير
        $admin_name = trim($_POST['admin_name']);
        $admin_email = trim($_POST['admin_email']);
        $admin_password = $_POST['admin_password'];
        $confirm_password = $_POST['confirm_password'];
        
        if (empty($admin_name) || empty($admin_email) || empty($admin_password)) {
            $error_message = 'يرجى ملء جميع الحقول';
        } elseif ($admin_password !== $confirm_password) {
            $error_message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
        } elseif (strlen($admin_password) < 6) {
            $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } else {
            // إنشاء حساب المدير
            require_once 'config/database.php';
            
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            
            $stmt = $conn->prepare("UPDATE users SET name = ?, email = ?, password = ? WHERE id = 1");
            $stmt->bind_param('sss', $admin_name, $admin_email, $hashed_password);
            
            if ($stmt->execute()) {
                $step = 4;
                $success_message = 'تم إنشاء حساب المدير بنجاح';
            } else {
                $error_message = 'حدث خطأ أثناء إنشاء حساب المدير';
            }
        }
    }
}

// التحقق من متطلبات النظام
$requirements = [
    'PHP Version >= 7.0' => version_compare(PHP_VERSION, '7.0.0', '>='),
    'MySQL Extension' => extension_loaded('mysqli'),
    'JSON Extension' => extension_loaded('json'),
    'Config Directory Writable' => is_writable('.') || is_writable('config'),
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت موقع CPA الاحترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin: 50px auto;
            max-width: 800px;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <h1><i class="fas fa-chart-line"></i> موقع CPA الاحترافي</h1>
                <p class="mb-0">معالج التثبيت</p>
            </div>
            
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step == 1 ? 'active' : 'completed') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step == 2 ? 'active' : 'completed') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step == 3 ? 'active' : 'completed') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? ($step == 4 ? 'active' : 'completed') : ''; ?>">4</div>
            </div>
            
            <div class="p-4">
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                    <!-- الخطوة 1: التحقق من المتطلبات -->
                    <h3>التحقق من متطلبات النظام</h3>
                    <p class="text-muted">يرجى التأكد من توفر جميع المتطلبات التالية:</p>
                    
                    <?php foreach ($requirements as $requirement => $status): ?>
                        <div class="requirement-item">
                            <span><?php echo $requirement; ?></span>
                            <span class="badge bg-<?php echo $status ? 'success' : 'danger'; ?>">
                                <i class="fas fa-<?php echo $status ? 'check' : 'times'; ?>"></i>
                                <?php echo $status ? 'متوفر' : 'غير متوفر'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                    
                    <form method="POST" class="mt-4">
                        <button type="submit" class="btn btn-primary" <?php echo !array_reduce($requirements, function($carry, $item) { return $carry && $item; }, true) ? 'disabled' : ''; ?>>
                            <i class="fas fa-arrow-left"></i> التالي
                        </button>
                    </form>
                    
                <?php elseif ($step == 2): ?>
                    <!-- الخطوة 2: إعداد قاعدة البيانات -->
                    <h3>إعداد قاعدة البيانات</h3>
                    <p class="text-muted">معلومات قاعدة البيانات InfinityFree (تم تعبئتها مسبقاً):</p>

                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> تم تعبئة معلومات قاعدة البيانات الخاصة بـ InfinityFree مسبقاً.
                        إذا كانت معلوماتك مختلفة، يرجى تعديلها أدناه.
                    </div>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="db_host" class="form-label">مضيف قاعدة البيانات</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" value="sql303.infinityfree.com" required>
                        </div>

                        <div class="mb-3">
                            <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" value="if0_39395085_q12" required>
                        </div>

                        <div class="mb-3">
                            <label for="db_user" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" value="if0_39395085" required>
                        </div>

                        <div class="mb-3">
                            <label for="db_pass" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass" value="Qweeee12">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-database"></i> إنشاء قاعدة البيانات
                        </button>
                    </form>
                    
                <?php elseif ($step == 3): ?>
                    <!-- الخطوة 3: إنشاء حساب المدير -->
                    <h3>إنشاء حساب المدير</h3>
                    <p class="text-muted">أنشئ حساب المدير الرئيسي:</p>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="admin_name" class="form-label">اسم المدير</label>
                            <input type="text" class="form-control" id="admin_name" name="admin_name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="admin_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="admin_password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> إنشاء حساب المدير
                        </button>
                    </form>
                    
                <?php elseif ($step == 4): ?>
                    <!-- الخطوة 4: اكتمال التثبيت -->
                    <div class="text-center">
                        <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                        <h3 class="text-success">تم التثبيت بنجاح!</h3>
                        <p class="text-muted mb-4">تم تثبيت موقع CPA الاحترافي بنجاح. يمكنك الآن البدء في استخدام الموقع.</p>
                        
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>مهم:</strong> يرجى حذف ملف install.php من الخادم لأسباب أمنية.
                        </div>

                        <div class="alert alert-success" role="alert">
                            <h5 class="alert-heading">معلومات مهمة:</h5>
                            <hr>
                            <p><strong>رابط الموقع العام:</strong> <code>yourdomain.infinityfree.net/public/</code></p>
                            <p><strong>رابط لوحة التحكم:</strong> <code>yourdomain.infinityfree.net/</code></p>
                            <p><strong>قاعدة البيانات:</strong> if0_39395085_q12</p>
                            <p class="mb-0"><strong>مضيف قاعدة البيانات:</strong> sql303.infinityfree.com</p>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="index.php" class="btn btn-primary btn-lg me-md-2">
                                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                            </a>
                            <a href="public/index.php" class="btn btn-success btn-lg">
                                <i class="fas fa-globe"></i> الموقع العام
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
