<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// التحقق من وجود معرف العرض
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$offer_id = (int)$_GET['id'];

// جلب بيانات العرض
$sql = "SELECT * FROM offers WHERE id = ? AND user_id = ?";
$offer = fetchRow($sql, 'ii', [$offer_id, $user_id]);

// التحقق من وجود العرض
if (!$offer) {
    header('Location: index.php');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة تحديث العرض
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $original_url = trim($_POST['original_url']);
    $commission = floatval($_POST['commission']);
    $category = trim($_POST['category']);
    $requirements = trim($_POST['requirements']);
    $status = $_POST['status'];
    
    // التحقق من صحة البيانات
    if (empty($title) || empty($original_url) || $commission <= 0) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!filter_var($original_url, FILTER_VALIDATE_URL)) {
        $error_message = 'رابط العرض غير صحيح';
    } else {
        // تحديث العرض
        $sql = "UPDATE offers SET 
                    title = ?, 
                    description = ?, 
                    original_url = ?, 
                    commission = ?, 
                    category = ?, 
                    requirements = ?, 
                    status = ?,
                    updated_at = NOW()
                WHERE id = ? AND user_id = ?";
        
        $stmt = executePreparedQuery($sql, 'sssdsssii', [
            $title, $description, $original_url, 
            $commission, $category, $requirements, $status,
            $offer_id, $user_id
        ]);
        
        if ($stmt && getAffectedRows() > 0) {
            $success_message = 'تم تحديث العرض بنجاح';
            // تحديث البيانات المحلية
            $offer['title'] = $title;
            $offer['description'] = $description;
            $offer['original_url'] = $original_url;
            $offer['commission'] = $commission;
            $offer['category'] = $category;
            $offer['requirements'] = $requirements;
            $offer['status'] = $status;
        } else {
            $error_message = 'حدث خطأ أثناء تحديث العرض';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل العرض - موقع CPA الاحترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تعديل العرض</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="view.php?id=<?php echo $offer_id; ?>" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                            <a href="index.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج تعديل العرض -->
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-edit"></i> تعديل معلومات العرض
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">عنوان العرض *</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="<?php echo htmlspecialchars($offer['title']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="commission" class="form-label">العمولة (بالدولار) *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="commission" name="commission" 
                                                   step="0.01" min="0" value="<?php echo $offer['commission']; ?>" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف العرض</label>
                                <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($offer['description']); ?></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="original_url" class="form-label">رابط العرض الأصلي *</label>
                                        <input type="url" class="form-control" id="original_url" name="original_url" 
                                               value="<?php echo htmlspecialchars($offer['original_url']); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="category" class="form-label">الفئة</label>
                                        <select class="form-select" id="category" name="category">
                                            <option value="">اختر الفئة</option>
                                            <option value="ترفيه" <?php echo $offer['category'] == 'ترفيه' ? 'selected' : ''; ?>>ترفيه</option>
                                            <option value="تسوق" <?php echo $offer['category'] == 'تسوق' ? 'selected' : ''; ?>>تسوق</option>
                                            <option value="تعليم" <?php echo $offer['category'] == 'تعليم' ? 'selected' : ''; ?>>تعليم</option>
                                            <option value="صحة" <?php echo $offer['category'] == 'صحة' ? 'selected' : ''; ?>>صحة</option>
                                            <option value="تقنية" <?php echo $offer['category'] == 'تقنية' ? 'selected' : ''; ?>>تقنية</option>
                                            <option value="مالية" <?php echo $offer['category'] == 'مالية' ? 'selected' : ''; ?>>مالية</option>
                                            <option value="أخرى" <?php echo $offer['category'] == 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="requirements" class="form-label">متطلبات العرض</label>
                                <textarea class="form-control" id="requirements" name="requirements" rows="3"><?php echo htmlspecialchars($offer['requirements']); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="status" class="form-label">حالة العرض</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo $offer['status'] == 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo $offer['status'] == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="view.php?id=<?php echo $offer_id; ?>" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-info-circle"></i> معلومات العرض
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($offer['created_at'])); ?></p>
                                <p><strong>آخر تحديث:</strong> <?php echo date('Y-m-d H:i', strtotime($offer['updated_at'])); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>معرف العرض:</strong> #<?php echo $offer['id']; ?></p>
                                <p><strong>الحالة الحالية:</strong> 
                                    <span class="badge bg-<?php echo $offer['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo $offer['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
