# تحسينات الأداء والأمان لموقع CPA الاحترافي

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# إعادة توجيه HTTP إلى HTTPS (يتم تفعيله عند الرفع على الاستضافة)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www إلى non-www (يتم تفعيله عند الرفع على الاستضافة)
# RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
# RewriteRule ^(.*)$ http://%1/$1 [R=301,L]

# منع الوصول إلى الملفات الحساسة
<FilesMatch "^(\.htaccess|\.htpasswd|\.git|\.env|\.gitignore|config\.php|database\.php)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى مجلد config
<IfModule mod_rewrite.c>
    RewriteRule ^config/ - [F,L]
</IfModule>

# تعيين منطقة زمنية افتراضية
php_value date.timezone "Asia/Riyadh"

# تحسين الأداء
# ضغط المحتوى
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# تخزين مؤقت للمتصفح
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/json "access plus 1 day"
    ExpiresByType application/xml "access plus 1 day"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# تحسينات الأمان
# منع عرض محتويات المجلدات
Options -Indexes

# منع تنفيذ البرامج النصية في مجلدات التحميل
<IfModule mod_rewrite.c>
    RewriteRule ^uploads/.*\.(php|pl|py|jsp|asp|htm|html|shtml|sh|cgi)$ - [F,L]
</IfModule>

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# تحسينات إضافية للاستضافة المجانية
# تقليل استخدام الموارد
php_value memory_limit 64M
php_value max_execution_time 30
php_value upload_max_filesize 8M
php_value post_max_size 8M

# تعطيل وظائف خطيرة (يمكن تفعيلها حسب الحاجة)
# php_value disable_functions exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source

# تحسينات SEO
# إعادة توجيه الصفحات غير الموجودة إلى الصفحة الرئيسية
ErrorDocument 404 /index.php

# تعامل مع الروابط المكسورة
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ /index.php [L]
</IfModule>

# تحسينات الأداء الإضافية
# تعطيل ETag
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# تحسين ضغط الصور
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_ACCEPT} image/webp
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^(.+)\.(jpe?g|png)$ $1.webp [T=image/webp,E=accept:1]
</IfModule>

<IfModule mod_headers.c>
    Header append Vary Accept env=REDIRECT_accept
</IfModule>

# تحسينات أمان إضافية
# منع الوصول إلى ملفات النسخ الاحتياطي
<FilesMatch "(\.(bak|config|sql|fla|psd|ini|log|sh|inc|swp|dist)|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# منع تصفح المجلدات الحساسة
<IfModule mod_rewrite.c>
    RewriteRule ^(includes|config|database) - [F,L]
</IfModule>
