<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$error_message = '';
$success_message = '';

// معالجة إضافة العرض
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $original_url = trim($_POST['original_url']);
    $commission = floatval($_POST['commission']);
    $category = trim($_POST['category']);
    $requirements = trim($_POST['requirements']);
    $status = $_POST['status'];
    
    // التحقق من صحة البيانات
    if (empty($title) || empty($original_url) || $commission <= 0) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!filter_var($original_url, FILTER_VALIDATE_URL)) {
        $error_message = 'رابط العرض غير صحيح';
    } else {
        // إدراج العرض الجديد
        $sql = "INSERT INTO offers (user_id, title, description, original_url, commission, category, requirements, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = executePreparedQuery($sql, 'issdssss', [
            $user_id, $title, $description, $original_url, 
            $commission, $category, $requirements, $status
        ]);
        
        if ($stmt) {
            $success_message = 'تم إضافة العرض بنجاح';
            // إعادة تعيين النموذج
            $title = $description = $original_url = $category = $requirements = '';
            $commission = 0;
            $status = 'active';
        } else {
            $error_message = 'حدث خطأ أثناء إضافة العرض';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عرض جديد - موقع CPA الاحترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إضافة عرض جديد</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="index.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- نموذج إضافة العرض -->
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-plus"></i> معلومات العرض الجديد
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">عنوان العرض *</label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="<?php echo htmlspecialchars($title ?? ''); ?>" required>
                                        <div class="form-text">اختر عنواناً جذاباً ووصفياً للعرض</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="commission" class="form-label">العمولة (بالدولار) *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="commission" name="commission" 
                                                   step="0.01" min="0" value="<?php echo $commission ?? ''; ?>" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف العرض</label>
                                <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($description ?? ''); ?></textarea>
                                <div class="form-text">اكتب وصفاً مفصلاً عن العرض وما يحصل عليه المستخدم</div>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="original_url" class="form-label">رابط العرض الأصلي *</label>
                                        <input type="url" class="form-control" id="original_url" name="original_url" 
                                               value="<?php echo htmlspecialchars($original_url ?? ''); ?>" required>
                                        <div class="form-text">الرابط الذي سيتم توجيه الزوار إليه</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="category" class="form-label">الفئة</label>
                                        <select class="form-select" id="category" name="category">
                                            <option value="">اختر الفئة</option>
                                            <option value="ترفيه" <?php echo (isset($category) && $category == 'ترفيه') ? 'selected' : ''; ?>>ترفيه</option>
                                            <option value="تسوق" <?php echo (isset($category) && $category == 'تسوق') ? 'selected' : ''; ?>>تسوق</option>
                                            <option value="تعليم" <?php echo (isset($category) && $category == 'تعليم') ? 'selected' : ''; ?>>تعليم</option>
                                            <option value="صحة" <?php echo (isset($category) && $category == 'صحة') ? 'selected' : ''; ?>>صحة</option>
                                            <option value="تقنية" <?php echo (isset($category) && $category == 'تقنية') ? 'selected' : ''; ?>>تقنية</option>
                                            <option value="مالية" <?php echo (isset($category) && $category == 'مالية') ? 'selected' : ''; ?>>مالية</option>
                                            <option value="أخرى" <?php echo (isset($category) && $category == 'أخرى') ? 'selected' : ''; ?>>أخرى</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="requirements" class="form-label">متطلبات العرض</label>
                                <textarea class="form-control" id="requirements" name="requirements" rows="3"><?php echo htmlspecialchars($requirements ?? ''); ?></textarea>
                                <div class="form-text">اذكر الشروط والمتطلبات للحصول على العمولة</div>
                            </div>

                            <div class="mb-3">
                                <label for="status" class="form-label">حالة العرض</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo (isset($status) && $status == 'active') ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo (isset($status) && $status == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="index.php" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ العرض
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- نصائح -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-lightbulb"></i> نصائح لإنشاء عرض ناجح
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> اختر عنواناً جذاباً ووصفياً</li>
                            <li><i class="fas fa-check text-success"></i> اكتب وصفاً مفصلاً وواضحاً</li>
                            <li><i class="fas fa-check text-success"></i> حدد عمولة مناسبة ومجزية</li>
                            <li><i class="fas fa-check text-success"></i> اذكر المتطلبات بوضوح</li>
                            <li><i class="fas fa-check text-success"></i> تأكد من صحة الرابط</li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
