<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من وجود رمز التتبع
if (!isset($_GET['c']) || empty($_GET['c'])) {
    header('HTTP/1.0 404 Not Found');
    exit('رمز التتبع غير صحيح');
}

$tracking_code = $_GET['c'];

// جلب معلومات العرض من رمز التتبع
$sql = "SELECT tc.offer_id, tc.user_id, o.original_url, o.title, o.status 
        FROM tracking_codes tc 
        JOIN offers o ON tc.offer_id = o.id 
        WHERE tc.code = ? AND o.status = 'active'";

$tracking = fetchRow($sql, 's', [$tracking_code]);

if (!$tracking) {
    header('HTTP/1.0 404 Not Found');
    exit('العرض غير موجود أو غير نشط');
}

// تسجيل النقرة
trackClick($tracking_code);

// تسجيل معلومات الزائر
$ip_address = $_SERVER['REMOTE_ADDR'];
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$referrer = $_SERVER['HTTP_REFERER'] ?? '';

$sql = "INSERT INTO visitors (ip_address, user_agent, referrer, tracking_code, visited_at) 
        VALUES (?, ?, ?, ?, NOW())";
executePreparedQuery($sql, 'ssss', [$ip_address, $user_agent, $referrer, $tracking_code]);

// إعادة توجيه المستخدم إلى الرابط الأصلي
header('Location: ' . $tracking['original_url']);
exit();
?>
