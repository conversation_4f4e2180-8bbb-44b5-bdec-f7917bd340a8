<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// معالجة حذف العرض
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $offer_id = (int)$_GET['delete'];
    
    // التحقق من أن العرض يخص المستخدم الحالي
    $sql = "DELETE FROM offers WHERE id = ? AND user_id = ?";
    $stmt = executePreparedQuery($sql, 'ii', [$offer_id, $user_id]);
    
    if ($stmt && getAffectedRows() > 0) {
        $success_message = 'تم حذف العرض بنجاح';
    } else {
        $error_message = 'فشل في حذف العرض';
    }
}

// جلب جميع العروض للمستخدم
$sql = "SELECT 
            o.id, 
            o.title, 
            o.commission, 
            o.category,
            o.status,
            o.created_at,
            COALESCE(SUM(s.clicks), 0) as total_clicks,
            COALESCE(SUM(s.conversions), 0) as total_conversions,
            COALESCE(SUM(o.commission * s.conversions), 0) as total_earnings
        FROM 
            offers o
        LEFT JOIN 
            statistics s ON o.id = s.offer_id
        WHERE 
            o.user_id = ?
        GROUP BY 
            o.id
        ORDER BY 
            o.created_at DESC";

$offers = fetchAll($sql, 'i', [$user_id]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض - موقع CPA الاحترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة العروض</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="add.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> إضافة عرض جديد
                            </a>
                        </div>
                    </div>
                </div>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- جدول العروض -->
                <div class="card shadow">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-gift"></i> قائمة العروض (<?php echo count($offers); ?>)
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($offers)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد عروض حتى الآن</h5>
                                <p class="text-muted">ابدأ بإضافة عرض جديد لبدء كسب المال</p>
                                <a href="add.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة عرض جديد
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>العرض</th>
                                            <th>الفئة</th>
                                            <th>العمولة</th>
                                            <th>النقرات</th>
                                            <th>التحويلات</th>
                                            <th>الأرباح</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($offers as $offer): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($offer['title']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo htmlspecialchars($offer['category'] ?: 'غير محدد'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    $<?php echo number_format($offer['commission'], 2); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo number_format($offer['total_clicks']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?php echo number_format($offer['total_conversions']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <strong class="text-success">
                                                    $<?php echo number_format($offer['total_earnings'], 2); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $offer['status'] == 'active' ? 'success' : 'secondary'; ?>">
                                                    <?php echo $offer['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('Y-m-d', strtotime($offer['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="view.php?id=<?php echo $offer['id']; ?>" 
                                                       class="btn btn-sm btn-info" title="عرض">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit.php?id=<?php echo $offer['id']; ?>" 
                                                       class="btn btn-sm btn-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="?delete=<?php echo $offer['id']; ?>" 
                                                       class="btn btn-sm btn-danger" title="حذف"
                                                       onclick="return confirm('هل أنت متأكد من حذف هذا العرض؟')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
