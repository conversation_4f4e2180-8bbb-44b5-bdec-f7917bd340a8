/**
 * ملف JavaScript الرئيسي لموقع CPA الاحترافي
 */

// انتظار تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل التلميحات
    enableTooltips();
    
    // تفعيل النوافذ المنبثقة
    enablePopovers();
    
    // تفعيل تأثيرات التمرير
    enableSmoothScroll();
    
    // تفعيل نسخ روابط التتبع
    enableTrackingLinkCopy();
    
    // تفعيل التحقق من النماذج
    enableFormValidation();
    
    // تفعيل تحميل الصور بكسل
    lazyLoadImages();
    
    // تحسين الأداء
    optimizePerformance();
});

/**
 * تفعيل التلميحات
 */
function enableTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تفعيل النوافذ المنبثقة
 */
function enablePopovers() {
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * تفعيل تأثيرات التمرير
 */
function enableSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * تفعيل نسخ روابط التتبع
 */
function enableTrackingLinkCopy() {
    const copyButtons = document.querySelectorAll('.copy-tracking-link');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const linkInput = document.getElementById(this.dataset.target);
            
            if (linkInput) {
                linkInput.select();
                linkInput.setSelectionRange(0, 99999);
                document.execCommand('copy');
                
                // تغيير نص الزر مؤقتًا
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            }
        });
    });
}

/**
 * تفعيل التحقق من النماذج
 */
function enableFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
}

/**
 * تحميل الصور بكسل
 */
function lazyLoadImages() {
    if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[loading="lazy"]');
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    } else {
        // Fallback for browsers that don't support lazy loading
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.3.2/lazysizes.min.js';
        document.body.appendChild(script);
    }
}

/**
 * تحسين الأداء
 */
function optimizePerformance() {
    // تقليل عدد طلبات HTTP
    deferNonCriticalStyles();
    
    // تقليل استخدام الذاكرة
    cleanupUnusedElements();
    
    // تحسين التفاعل
    debounceEvents();
}

/**
 * تأجيل تحميل الأنماط غير الضرورية
 */
function deferNonCriticalStyles() {
    const nonCriticalStyles = document.querySelectorAll('link[rel="stylesheet"][data-priority="low"]');
    
    nonCriticalStyles.forEach(link => {
        link.setAttribute('media', 'print');
        link.setAttribute('onload', "this.media='all'");
    });
}

/**
 * تنظيف العناصر غير المستخدمة
 */
function cleanupUnusedElements() {
    // إزالة العناصر المخفية بعد فترة
    setTimeout(() => {
        const hiddenElements = document.querySelectorAll('.d-none, [style*="display: none"]');
        
        hiddenElements.forEach(el => {
            if (!el.classList.contains('keep-in-dom')) {
                el.remove();
            }
        });
    }, 5000);
}

/**
 * تقليل تكرار الأحداث
 */
function debounceEvents() {
    // تقليل أحداث التمرير
    let scrollTimeout;
    
    window.addEventListener('scroll', function() {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        
        scrollTimeout = setTimeout(function() {
            // تنفيذ الوظائف المرتبطة بالتمرير هنا
        }, 100);
    });
    
    // تقليل أحداث تغيير الحجم
    let resizeTimeout;
    
    window.addEventListener('resize', function() {
        if (resizeTimeout) {
            clearTimeout(resizeTimeout);
        }
        
        resizeTimeout = setTimeout(function() {
            // تنفيذ الوظائف المرتبطة بتغيير الحجم هنا
        }, 100);
    });
}

/**
 * نسخ رابط التتبع
 * 
 * @param {string} elementId معرف عنصر الإدخال
 */
function copyTrackingLink(elementId) {
    const copyText = document.getElementById(elementId);
    
    if (copyText) {
        copyText.select();
        copyText.setSelectionRange(0, 99999);
        document.execCommand("copy");
        
        alert("تم نسخ الرابط: " + copyText.value);
    }
}

/**
 * تأكيد الحذف
 * 
 * @param {string} message رسالة التأكيد
 * @returns {boolean} نتيجة التأكيد
 */
function confirmDelete(message) {
    return confirm(message || 'هل أنت متأكد من الحذف؟');
}

/**
 * تحديث حالة العرض
 * 
 * @param {number} offerId معرف العرض
 * @param {string} status الحالة الجديدة
 */
function updateOfferStatus(offerId, status) {
    // يمكن استخدام AJAX لتحديث حالة العرض
    fetch('offers/update_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `offer_id=${offerId}&status=${status}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث واجهة المستخدم
            const statusBadge = document.querySelector(`#offer-${offerId} .status-badge`);
            
            if (statusBadge) {
                statusBadge.className = `badge bg-${status === 'active' ? 'success' : 'secondary'} status-badge`;
                statusBadge.textContent = status === 'active' ? 'نشط' : 'غير نشط';
            }
        } else {
            alert('حدث خطأ أثناء تحديث حالة العرض');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء الاتصال بالخادم');
    });
}

// تصدير الدوال للاستخدام العام
window.copyTrackingLink = copyTrackingLink;
window.confirmDelete = confirmDelete;
window.updateOfferStatus = updateOfferStatus;
