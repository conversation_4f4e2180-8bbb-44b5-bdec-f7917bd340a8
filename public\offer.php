<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من وجود معرف العرض
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$offer_id = (int)$_GET['id'];

// جلب بيانات العرض
$sql = "SELECT 
            o.*, 
            u.name as publisher_name
        FROM 
            offers o
        JOIN 
            users u ON o.user_id = u.id
        WHERE 
            o.id = ? AND o.status = 'active'";

$offer = fetchRow($sql, 'i', [$offer_id]);

// التحقق من وجود العرض
if (!$offer) {
    header('Location: index.php');
    exit();
}

// إنشاء رابط التتبع
$tracking_link = generateTrackingLink($offer_id, $offer['user_id']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($offer['title']); ?> - موقع CPA الاحترافي</title>
    <meta name="description" content="<?php echo htmlspecialchars(substr($offer['description'], 0, 160)); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding-top: 70px;
        }
        .offer-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        .offer-image {
            height: 300px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 5rem;
            color: #999;
            border-radius: 15px;
        }
        .commission-highlight {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        .btn-get-offer {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            border-radius: 25px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        .btn-get-offer:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        .feature-item {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .feature-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        .requirements-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-chart-line"></i> CPA Pro
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">العروض</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#categories">الفئات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#about">حولنا</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../login.php">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- قسم العرض الرئيسي -->
    <section class="offer-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php" class="text-light">الرئيسية</a></li>
                            <li class="breadcrumb-item active text-light">تفاصيل العرض</li>
                        </ol>
                    </nav>
                    
                    <h1 class="display-4 fw-bold mb-3"><?php echo htmlspecialchars($offer['title']); ?></h1>
                    
                    <div class="d-flex align-items-center mb-4">
                        <?php if ($offer['category']): ?>
                            <span class="badge bg-light text-dark me-3 px-3 py-2">
                                <i class="fas fa-tag"></i> <?php echo htmlspecialchars($offer['category']); ?>
                            </span>
                        <?php endif; ?>
                        <span class="text-light">
                            <i class="fas fa-user"></i> بواسطة <?php echo htmlspecialchars($offer['publisher_name']); ?>
                        </span>
                    </div>
                    
                    <p class="lead mb-4"><?php echo nl2br(htmlspecialchars($offer['description'])); ?></p>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="commission-highlight">
                        <h3 class="mb-2">احصل على</h3>
                        <div class="display-3 fw-bold">$<?php echo number_format($offer['commission'], 2); ?></div>
                        <p class="mb-0">عند إتمام العرض</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- تفاصيل العرض -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- صورة العرض -->
                    <div class="offer-image mb-4">
                        <?php if ($offer['image_url']): ?>
                            <img src="<?php echo htmlspecialchars($offer['image_url']); ?>" 
                                 alt="<?php echo htmlspecialchars($offer['title']); ?>" 
                                 class="img-fluid rounded">
                        <?php else: ?>
                            <i class="fas fa-gift"></i>
                        <?php endif; ?>
                    </div>

                    <!-- وصف مفصل -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> تفاصيل العرض</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text"><?php echo nl2br(htmlspecialchars($offer['description'])); ?></p>
                        </div>
                    </div>

                    <!-- المتطلبات -->
                    <?php if ($offer['requirements']): ?>
                    <div class="requirements-list mb-4">
                        <h5 class="text-warning mb-3">
                            <i class="fas fa-exclamation-triangle"></i> متطلبات العرض
                        </h5>
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($offer['requirements'])); ?></p>
                    </div>
                    <?php endif; ?>

                    <!-- المميزات -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h6>آمن ومضمون</h6>
                                <p class="text-muted small">عرض موثوق ومفحوص</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <h6>سريع وسهل</h6>
                                <p class="text-muted small">عملية بسيطة وسريعة</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <h6>عمولة مجزية</h6>
                                <p class="text-muted small">احصل على أفضل العمولات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- بطاقة الحصول على العرض -->
                    <div class="card shadow-lg sticky-top" style="top: 100px;">
                        <div class="card-body text-center p-4">
                            <div class="commission-highlight mb-4">
                                <h4 class="mb-2">العمولة</h4>
                                <div class="display-4 fw-bold">$<?php echo number_format($offer['commission'], 2); ?></div>
                            </div>
                            
                            <a href="<?php echo $tracking_link; ?>" 
                               class="btn btn-get-offer btn-success btn-lg w-100 mb-3"
                               target="_blank">
                                <i class="fas fa-external-link-alt"></i> احصل على العرض الآن
                            </a>
                            
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle"></i>
                                <small>سيتم توجيهك إلى الموقع الرسمي لإتمام العرض</small>
                            </div>
                            
                            <hr>
                            
                            <div class="text-start">
                                <h6 class="fw-bold mb-3">معلومات إضافية:</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-calendar text-muted"></i>
                                        <small class="text-muted ms-2">
                                            تاريخ النشر: <?php echo date('Y-m-d', strtotime($offer['created_at'])); ?>
                                        </small>
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-user text-muted"></i>
                                        <small class="text-muted ms-2">
                                            الناشر: <?php echo htmlspecialchars($offer['publisher_name']); ?>
                                        </small>
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success"></i>
                                        <small class="text-muted ms-2">عرض مفعل ونشط</small>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer class="bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-chart-line"></i> CPA Pro</h5>
                    <p class="mb-0">منصة العروض الاحترافية</p>
                    <small class="text-muted">© <?php echo date('Y'); ?> جميع الحقوق محفوظة</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
