<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// تحديد الفترة الزمنية
$period = isset($_GET['period']) ? $_GET['period'] : 'month';

switch ($period) {
    case 'week':
        $date_range = "AND s.date >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)";
        $group_by = "GROUP BY s.date";
        $period_title = "الأسبوع الماضي";
        break;
    case 'month':
        $date_range = "AND s.date >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)";
        $group_by = "GROUP BY s.date";
        $period_title = "الشهر الماضي";
        break;
    case 'year':
        $date_range = "AND s.date >= DATE_SUB(CURRENT_DATE, INTERVAL 1 YEAR)";
        $group_by = "GROUP BY MONTH(s.date)";
        $period_title = "السنة الماضية";
        break;
    case 'all':
        $date_range = "";
        $group_by = "GROUP BY s.date";
        $period_title = "كل الفترات";
        break;
    default:
        $date_range = "AND s.date >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)";
        $group_by = "GROUP BY s.date";
        $period_title = "الشهر الماضي";
}

// جلب إحصائيات المستخدم حسب التاريخ
$sql = "SELECT 
            s.date,
            SUM(s.clicks) as daily_clicks,
            SUM(s.conversions) as daily_conversions,
            SUM(o.commission * s.conversions) as daily_earnings
        FROM 
            statistics s
        JOIN 
            offers o ON s.offer_id = o.id
        WHERE 
            o.user_id = ? $date_range
        $group_by
        ORDER BY 
            s.date DESC";

$statistics = fetchAll($sql, 'i', [$user_id]);

// جلب إحصائيات المستخدم حسب العرض
$sql = "SELECT 
            o.id,
            o.title,
            o.commission,
            SUM(s.clicks) as total_clicks,
            SUM(s.conversions) as total_conversions,
            SUM(o.commission * s.conversions) as total_earnings
        FROM 
            statistics s
        JOIN 
            offers o ON s.offer_id = o.id
        WHERE 
            o.user_id = ? $date_range
        GROUP BY 
            o.id
        ORDER BY 
            total_earnings DESC";

$offer_stats = fetchAll($sql, 'i', [$user_id]);

// حساب الإجماليات
$total_clicks = 0;
$total_conversions = 0;
$total_earnings = 0;

foreach ($statistics as $stat) {
    $total_clicks += $stat['daily_clicks'];
    $total_conversions += $stat['daily_conversions'];
    $total_earnings += $stat['daily_earnings'];
}

$conversion_rate = $total_clicks > 0 ? ($total_conversions / $total_clicks) * 100 : 0;

// تحضير بيانات الرسم البياني
$chart_dates = [];
$chart_clicks = [];
$chart_conversions = [];
$chart_earnings = [];

foreach (array_reverse($statistics) as $stat) {
    $chart_dates[] = $stat['date'];
    $chart_clicks[] = $stat['daily_clicks'];
    $chart_conversions[] = $stat['daily_conversions'];
    $chart_earnings[] = $stat['daily_earnings'];
}

$chart_dates_json = json_encode($chart_dates);
$chart_clicks_json = json_encode($chart_clicks);
$chart_conversions_json = json_encode($chart_conversions);
$chart_earnings_json = json_encode($chart_earnings);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإحصائيات - موقع CPA الاحترافي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include 'includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">الإحصائيات</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="?period=week" class="btn btn-sm btn-outline-secondary <?php echo $period == 'week' ? 'active' : ''; ?>">
                                أسبوع
                            </a>
                            <a href="?period=month" class="btn btn-sm btn-outline-secondary <?php echo $period == 'month' ? 'active' : ''; ?>">
                                شهر
                            </a>
                            <a href="?period=year" class="btn btn-sm btn-outline-secondary <?php echo $period == 'year' ? 'active' : ''; ?>">
                                سنة
                            </a>
                            <a href="?period=all" class="btn btn-sm btn-outline-secondary <?php echo $period == 'all' ? 'active' : ''; ?>">
                                الكل
                            </a>
                        </div>
                    </div>
                </div>

                <!-- ملخص الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي النقرات (<?php echo $period_title; ?>)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_clicks); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            إجمالي التحويلات (<?php echo $period_title; ?>)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_conversions); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            معدل التحويل (<?php echo $period_title; ?>)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($conversion_rate, 2); ?>%
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-percentage fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            إجمالي الأرباح (<?php echo $period_title; ?>)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            $<?php echo number_format($total_earnings, 2); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسم البياني -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-chart-line"></i> تحليل الأداء (<?php echo $period_title; ?>)
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($statistics)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد بيانات للعرض</h5>
                                <p class="text-muted">ابدأ بالترويج للعروض لرؤية الإحصائيات هنا</p>
                            </div>
                        <?php else: ?>
                            <canvas id="performanceChart" height="300"></canvas>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- إحصائيات العروض -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-gift"></i> أداء العروض (<?php echo $period_title; ?>)
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($offer_stats)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد بيانات للعروض</h5>
                                <p class="text-muted">ابدأ بالترويج للعروض لرؤية الإحصائيات هنا</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>العرض</th>
                                            <th>العمولة</th>
                                            <th>النقرات</th>
                                            <th>التحويلات</th>
                                            <th>معدل التحويل</th>
                                            <th>الأرباح</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($offer_stats as $stat): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($stat['title']); ?></td>
                                            <td>$<?php echo number_format($stat['commission'], 2); ?></td>
                                            <td><?php echo number_format($stat['total_clicks']); ?></td>
                                            <td><?php echo number_format($stat['total_conversions']); ?></td>
                                            <td>
                                                <?php 
                                                $rate = $stat['total_clicks'] > 0 ? ($stat['total_conversions'] / $stat['total_clicks']) * 100 : 0;
                                                echo number_format($rate, 2) . '%';
                                                ?>
                                            </td>
                                            <td class="text-success">
                                                $<?php echo number_format($stat['total_earnings'], 2); ?>
                                            </td>
                                            <td>
                                                <a href="offers/view.php?id=<?php echo $stat['id']; ?>" 
                                                   class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <?php if (!empty($statistics)): ?>
    <script>
        // إعداد الرسم البياني
        const ctx = document.getElementById('performanceChart').getContext('2d');
        const performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: <?php echo $chart_dates_json; ?>,
                datasets: [
                    {
                        label: 'النقرات',
                        data: <?php echo $chart_clicks_json; ?>,
                        backgroundColor: 'rgba(78, 115, 223, 0.05)',
                        borderColor: 'rgba(78, 115, 223, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                        pointBorderColor: '#fff',
                        pointRadius: 3,
                        tension: 0.3
                    },
                    {
                        label: 'التحويلات',
                        data: <?php echo $chart_conversions_json; ?>,
                        backgroundColor: 'rgba(28, 200, 138, 0.05)',
                        borderColor: 'rgba(28, 200, 138, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(28, 200, 138, 1)',
                        pointBorderColor: '#fff',
                        pointRadius: 3,
                        tension: 0.3
                    },
                    {
                        label: 'الأرباح ($)',
                        data: <?php echo $chart_earnings_json; ?>,
                        backgroundColor: 'rgba(246, 194, 62, 0.05)',
                        borderColor: 'rgba(246, 194, 62, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(246, 194, 62, 1)',
                        pointBorderColor: '#fff',
                        pointRadius: 3,
                        tension: 0.3,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            borderDash: [2, 2]
                        },
                        title: {
                            display: true,
                            text: 'النقرات / التحويلات'
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'الأرباح ($)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    </script>
    <?php endif; ?>
</body>
</html>
