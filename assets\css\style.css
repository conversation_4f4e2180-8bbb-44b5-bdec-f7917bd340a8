/* ملف الأنماط الرئيسي لموقع CPA الاحترافي */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* إعدادات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* تحسينات Bootstrap */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-1px);
}

.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* شريط التنقل */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* الشريط الجانبي */
.sidebar {
    background-color: #fff;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: #666;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    margin: 2px 10px;
    transition: var(--transition);
}

.sidebar .nav-link:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.sidebar .nav-link i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* بطاقات الإحصائيات */
.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-left-info {
    border-left: 4px solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* الأزرار المخصصة */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    border: none;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(231, 74, 59, 0.1));
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(102, 126, 234, 0.1));
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 235, 59, 0.1));
    color: #856404;
}

/* الشارات */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 20px;
}

/* النماذج */
.form-label {
    font-weight: 600;
    color: #555;
    margin-bottom: 0.5rem;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ddd;
    color: #666;
}

/* التحميل والرسوم المتحركة */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .btn-toolbar {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    main {
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* تأثيرات خاصة */
.hover-shadow {
    transition: var(--transition);
}

.hover-shadow:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسينات الأداء */
.card-img-top {
    object-fit: cover;
    height: 200px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* تحسينات إضافية للاستضافة المجانية */
.optimized-image {
    max-width: 100%;
    height: auto;
    loading: lazy;
}

.compressed-content {
    font-size: 0.9rem;
    line-height: 1.4;
}

/* تقليل استخدام الذاكرة */
.minimal-padding {
    padding: 0.5rem;
}

.minimal-margin {
    margin: 0.25rem;
}

/* تحسينات SEO */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
