<?php
/**
 * ملف الاتصال بقاعدة البيانات
 */

// معلومات الاتصال بقاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root'); // يجب تغييره عند الرفع على infinityfree
define('DB_PASS', ''); // يجب تغييره عند الرفع على infinityfree
define('DB_NAME', 'cpa_marketing');

// إنشاء اتصال بقاعدة البيانات
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}

// ضبط الترميز
$conn->set_charset("utf8mb4");

/**
 * دالة للحصول على اتصال قاعدة البيانات
 * 
 * @return mysqli اتصال قاعدة البيانات
 */
function getDbConnection() {
    global $conn;
    return $conn;
}

/**
 * دالة لتنفيذ استعلام قاعدة البيانات
 * 
 * @param string $sql استعلام SQL
 * @return mysqli_result|bool نتيجة الاستعلام
 */
function executeQuery($sql) {
    global $conn;
    return $conn->query($sql);
}

/**
 * دالة لتنفيذ استعلام محضر
 * 
 * @param string $sql استعلام SQL
 * @param string $types أنواع المعاملات (s للنصوص، i للأرقام الصحيحة، d للأرقام العشرية، b للبيانات الثنائية)
 * @param array $params مصفوفة المعاملات
 * @return mysqli_stmt|bool بيان محضر
 */
function executePreparedQuery($sql, $types, $params) {
    global $conn;
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        return false;
    }
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    
    return $stmt;
}

/**
 * دالة للحصول على صف واحد من نتيجة الاستعلام
 * 
 * @param string $sql استعلام SQL
 * @param string $types أنواع المعاملات (اختياري)
 * @param array $params مصفوفة المعاملات (اختياري)
 * @return array|null صف من البيانات أو null
 */
function fetchRow($sql, $types = '', $params = []) {
    global $conn;
    
    if (empty($params)) {
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }
    
    $stmt = executePreparedQuery($sql, $types, $params);
    
    if (!$stmt) {
        return null;
    }
    
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    
    $stmt->close();
    
    return null;
}

/**
 * دالة للحصول على مصفوفة من الصفوف من نتيجة الاستعلام
 * 
 * @param string $sql استعلام SQL
 * @param string $types أنواع المعاملات (اختياري)
 * @param array $params مصفوفة المعاملات (اختياري)
 * @return array مصفوفة من الصفوف
 */
function fetchAll($sql, $types = '', $params = []) {
    global $conn;
    
    $rows = [];
    
    if (empty($params)) {
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $rows[] = $row;
            }
        }
        
        return $rows;
    }
    
    $stmt = executePreparedQuery($sql, $types, $params);
    
    if (!$stmt) {
        return $rows;
    }
    
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $rows[] = $row;
        }
    }
    
    $stmt->close();
    
    return $rows;
}

/**
 * دالة للحصول على آخر معرف تم إدراجه
 * 
 * @return int آخر معرف تم إدراجه
 */
function getLastInsertId() {
    global $conn;
    return $conn->insert_id;
}

/**
 * دالة للحصول على عدد الصفوف المتأثرة
 * 
 * @return int عدد الصفوف المتأثرة
 */
function getAffectedRows() {
    global $conn;
    return $conn->affected_rows;
}

/**
 * دالة لتهريب النص لمنع حقن SQL
 * 
 * @param string $value النص المراد تهريبه
 * @return string النص المهرب
 */
function escapeString($value) {
    global $conn;
    return $conn->real_escape_string($value);
}
